# MY1和T1J MY1密码生成器 - 简洁版使用说明

## 文件说明

本应用提供了简洁版的密码生成器Excel文件，直接显示两个密码，不显示计算过程和SOP日期：

1. **密码生成器_简洁版.xml** - 美化版Excel文件（推荐）
2. **密码生成器_直接显示.csv** - 简洁版CSV格式，可直接在Excel中打开

## 使用方法

### 方法一：使用XML格式（推荐）

1. 将**密码生成器_简洁版.xml**文件复制到您的电脑
2. 双击打开文件，它会自动在Excel中打开
3. 如果提示，请选择"是"允许打开
4. 按F9键刷新计算结果，显示当前密码
5. 使用"另存为"功能保存为.xlsx或.xls格式

### 方法二：使用CSV格式

1. 将**密码生成器_直接显示.csv**文件复制到您的电脑
2. 双击打开文件，它会自动在Excel中打开
3. 如果Excel中显示的公式有错误，请检查分隔符是否正确
   - 可能需要将分号(;)替换为逗号(,)，或反之
4. 按F9键刷新计算结果，显示当前密码

## 特点

1. **简洁界面** - 直接显示两个密码，不显示计算过程和SOP日期
2. **美观设计** - 使用彩色背景和醒目字体，方便查看
3. **自动计算** - 根据当前日期和时间自动计算密码
4. **实时刷新** - 按F9键可随时刷新密码

## 密码说明

1. **MY1项目密码** - 用于加密项目的密码
2. **T1J MY1拨号密码** - 用于拨号的密码，格式为 *#密码#*

## 常见问题

### 密码不正确？

- 检查电脑系统时间是否正确
- 按F9键手动刷新计算结果

### 公式显示错误？

- 检查Excel中的公式分隔符设置（逗号或分号）
- 确保单元格格式设置为"文本"，以显示前导零

### 如何实现自动刷新？

如果需要自动刷新功能，请参考"密码生成器_完整版.txt"中的VBA实现说明，创建启用宏的Excel文件。 