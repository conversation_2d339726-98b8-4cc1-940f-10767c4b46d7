1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autolink.any"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\27002045da22e0ff64c1cb4d22885da0\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.autolink.any.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\27002045da22e0ff64c1cb4d22885da0\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\27002045da22e0ff64c1cb4d22885da0\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.autolink.any.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\27002045da22e0ff64c1cb4d22885da0\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\27002045da22e0ff64c1cb4d22885da0\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->E:\XM\any\app\src\main\AndroidManifest.xml:5:5-24:19
18        android:allowBackup="true"
18-->E:\XM\any\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\27002045da22e0ff64c1cb4d22885da0\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->E:\XM\any\app\src\main\AndroidManifest.xml:7:9-65
21        android:extractNativeLibs="false"
22        android:fullBackupContent="@xml/backup_rules"
22-->E:\XM\any\app\src\main\AndroidManifest.xml:8:9-54
23        android:icon="@mipmap/ic_launcher"
23-->E:\XM\any\app\src\main\AndroidManifest.xml:9:9-43
24        android:label="@string/app_name"
24-->E:\XM\any\app\src\main\AndroidManifest.xml:10:9-41
25        android:roundIcon="@mipmap/ic_launcher_round"
25-->E:\XM\any\app\src\main\AndroidManifest.xml:11:9-54
26        android:supportsRtl="true"
26-->E:\XM\any\app\src\main\AndroidManifest.xml:12:9-35
27        android:theme="@style/Theme.Any工厂模式密码" >
27-->E:\XM\any\app\src\main\AndroidManifest.xml:13:9-47
28        <activity
28-->E:\XM\any\app\src\main\AndroidManifest.xml:15:9-23:20
29            android:name="com.autolink.any.MainActivity"
29-->E:\XM\any\app\src\main\AndroidManifest.xml:16:13-41
30            android:exported="true" >
30-->E:\XM\any\app\src\main\AndroidManifest.xml:17:13-36
31            <intent-filter>
31-->E:\XM\any\app\src\main\AndroidManifest.xml:18:13-22:29
32                <action android:name="android.intent.action.MAIN" />
32-->E:\XM\any\app\src\main\AndroidManifest.xml:19:17-69
32-->E:\XM\any\app\src\main\AndroidManifest.xml:19:25-66
33
34                <category android:name="android.intent.category.LAUNCHER" />
34-->E:\XM\any\app\src\main\AndroidManifest.xml:21:17-77
34-->E:\XM\any\app\src\main\AndroidManifest.xml:21:27-74
35            </intent-filter>
36        </activity>
37
38        <provider
38-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
39            android:name="androidx.startup.InitializationProvider"
39-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
40            android:authorities="com.autolink.any.androidx-startup"
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
41            android:exported="false" >
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
42            <meta-data
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
43                android:name="androidx.emoji2.text.EmojiCompatInitializer"
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
44                android:value="androidx.startup" />
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39fa40fef6d187237310e6e3f4c6a80\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
45            <meta-data
45-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5504a2950219b3c86a1bd43bc66c8025\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
46-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5504a2950219b3c86a1bd43bc66c8025\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
47                android:value="androidx.startup" />
47-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5504a2950219b3c86a1bd43bc66c8025\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
49-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
50                android:value="androidx.startup" />
50-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
51        </provider>
52
53        <receiver
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
54            android:name="androidx.profileinstaller.ProfileInstallReceiver"
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
55            android:directBootAware="false"
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
56            android:enabled="true"
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
57            android:exported="true"
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
58            android:permission="android.permission.DUMP" >
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
59            <intent-filter>
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
60                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
61            </intent-filter>
62            <intent-filter>
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
63                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
64            </intent-filter>
65            <intent-filter>
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
66                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
67            </intent-filter>
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
69                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad2d430018ca6134529f6fb911f99c0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
70            </intent-filter>
71        </receiver>
72    </application>
73
74</manifest>
