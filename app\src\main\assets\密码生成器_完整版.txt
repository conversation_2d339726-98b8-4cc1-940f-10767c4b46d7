# MY1和T1J MY1密码生成器Excel完整实现指南

## 基础版本（仅使用公式）

### 工作表1: MY1密码生成器

在A1-B6单元格中设置以下内容：

| A列 | B列 |
|-----|-----|
| SOP日期 | 20250110 |
| 当前日期 | =TODAY() |
| 当前时间 | =NOW() |
| 月日时(MMddHH) | =VALUE(TEXT(B2,"mm")&TEXT(B2,"dd")&TEXT(B3,"hh")) |
| 计算结果 | =B1*B4 |
| MY1密码 | =RIGHT("000000"&MOD(B5,1000000),6) |

### 工作表2: T1J MY1密码生成器

在A1-B10单元格中设置以下内容：

| A列 | B列 |
|-----|-----|
| SOP日期 | 20250110 |
| 当前日期 | =TODAY() |
| 当前时间 | =NOW() |
| 月份 | =MONTH(B2) |
| 日期 | =DAY(B2) |
| 小时 | =HOUR(B3) |
| 构造数据(M*10000+D*100+H) | =B4*10000+B5*100+B6 |
| 计算结果(乘SOP) | =B7*B1 |
| 减去小时值 | =B8-B6 |
| 取模结果 | =MOD(MOD(B9,1000000000),1000000) |
| T1J MY1密码 | =RIGHT("000000"&B10,6) |
| 格式化密码 | ="*#"&B11&"#*" |

## 高级版本（使用VBA实现自动刷新）

### 1. 创建工作表

创建两个工作表，分别命名为"MY1密码"和"T1J_MY1密码"

### 2. 设计工作表布局

**MY1密码工作表：**
- A1: "MY1项目SOP密码生成器"
- A3: "SOP日期："
- B3: 20250110
- A4: "当前日期时间："
- B4: [由VBA自动更新]
- A5: "月日时(MMddHH)："
- B5: [由VBA自动更新]
- A6: "计算结果："
- B6: [由VBA自动更新]
- A7: "MY1密码："
- B7: [由VBA自动更新]

**T1J_MY1密码工作表：**
- A1: "T1J MY1拨号密码生成器"
- A3: "SOP日期："
- B3: 20250110
- A4: "当前日期时间："
- B4: [由VBA自动更新]
- A5: "月份："
- B5: [由VBA自动更新]
- A6: "日期："
- B6: [由VBA自动更新]
- A7: "小时："
- B7: [由VBA自动更新]
- A8: "构造数据："
- B8: [由VBA自动更新]
- A9: "计算结果："
- B9: [由VBA自动更新]
- A10: "T1J MY1密码："
- B10: [由VBA自动更新]
- A11: "格式化密码："
- B11: [由VBA自动更新]

### 3. VBA代码实现

按Alt+F11打开VBA编辑器，插入一个新模块，并添加以下代码：

```vba
Option Explicit

' 常量定义
Const SOP_DATE As Long = 20250110

' 自动刷新间隔（毫秒）
Const REFRESH_INTERVAL As Long = 10000 ' 10秒

' 主函数：更新所有密码
Public Sub UpdateAllPasswords()
    UpdateMY1Password
    UpdateT1JMY1Password
    
    ' 设置下一次自动刷新
    Application.OnTime Now + TimeValue("00:00:" & REFRESH_INTERVAL / 1000), "UpdateAllPasswords"
End Sub

' 更新MY1密码
Public Sub UpdateMY1Password()
    Dim ws As Worksheet
    Set ws = Worksheets("MY1密码")
    
    ' 更新当前日期时间
    ws.Range("B4").Value = Now
    
    ' 计算月日时(MMddHH)
    Dim monthDayHour As Long
    monthDayHour = Val(Format(Now, "MMddHH"))
    ws.Range("B5").Value = monthDayHour
    
    ' 计算结果
    Dim result As Double
    result = CDbl(SOP_DATE) * monthDayHour
    ws.Range("B6").Value = result
    
    ' 生成6位密码
    ws.Range("B7").Value = Right("000000" & (result Mod 1000000), 6)
End Sub

' 更新T1J MY1密码
Public Sub UpdateT1JMY1Password()
    Dim ws As Worksheet
    Set ws = Worksheets("T1J_MY1密码")
    
    ' 更新当前日期时间
    ws.Range("B4").Value = Now
    
    ' 获取月、日、小时
    Dim month As Integer, day As Integer, hour As Integer
    month = Month(Now)
    day = Day(Now)
    hour = Hour(Now)
    
    ws.Range("B5").Value = month
    ws.Range("B6").Value = day
    ws.Range("B7").Value = hour
    
    ' 构造数据
    Dim data As Long
    data = (month * 10000) + (day * 100) + hour
    ws.Range("B8").Value = data
    
    ' 计算结果
    Dim result As Double
    result = CDbl(data) * SOP_DATE
    result = result - hour
    result = result Mod 1000000000
    result = result Mod 1000000
    ws.Range("B9").Value = result
    
    ' 生成6位密码
    ws.Range("B10").Value = Right("000000" & result, 6)
    
    ' 格式化密码
    ws.Range("B11").Value = "*#" & Right("000000" & result, 6) & "#*"
End Sub

' 工作簿打开时自动启动
Public Sub Workbook_Open()
    UpdateAllPasswords
End Sub

' 手动启动刷新
Public Sub StartAutoRefresh()
    UpdateAllPasswords
End Sub

' 停止自动刷新
Public Sub StopAutoRefresh()
    On Error Resume Next
    Application.OnTime Now + TimeValue("00:00:" & REFRESH_INTERVAL / 1000), "UpdateAllPasswords", , False
    On Error GoTo 0
End Sub
```

### 4. 设置工作簿打开时自动运行

在ThisWorkbook模块中添加以下代码：

```vba
Private Sub Workbook_Open()
    Call StartAutoRefresh
End Sub

Private Sub Workbook_BeforeClose(Cancel As Boolean)
    Call StopAutoRefresh
End Sub
```

### 5. 添加手动刷新按钮（可选）

1. 在开发工具选项卡中，插入一个按钮控件
2. 将按钮放在工作表上
3. 右键点击按钮，选择"分配宏"
4. 选择"UpdateAllPasswords"宏

## 使用说明

1. 打开Excel文件后，密码会自动每10秒刷新一次
2. 可以点击刷新按钮立即更新密码
3. 密码会自动根据当前日期和时间计算
4. 关闭文件时会自动停止刷新

## 注意事项

1. 确保启用了宏功能（可能需要设置Excel安全级别）
2. 如果公式显示错误，检查日期和时间格式设置
3. 如需修改刷新间隔，更改VBA代码中的REFRESH_INTERVAL常量
4. 密码算法完全按照Java代码逻辑实现，确保结果一致性 