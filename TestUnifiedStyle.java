import java.util.Calendar;

public class TestUnifiedStyle {
    
    // SOP日期常量
    private static final long MY1_SOP_DATE = 20250110L;
    private static final long T1L_SOP_DATE = 20240910L;
    private static final long T1LINT_SOP_DATE = 20241130L;
    private static final long T1NINT_SOP_DATE = 20240830L;
    private static final long T1P_SOP_DATE = 20231030L;
    private static final long T1P_INT_SOP_DATE = 20240530L;
    
    public static void main(String[] args) {
        System.out.println("=== 测试统一样式显示 ===");
        System.out.println("当前时间: " + getCurrentTimeString());
        System.out.println();
        
        // 获取当前时间信息
        int[] dateHourInfo = getDateHourDigit();
        
        // 测试所有项目的密码（统一样式）
        System.out.println("所有项目密码显示（统一样式）:");
        System.out.println();
        
        testProject("MY1", MY1_SOP_DATE, dateHourInfo);
        testProject("T1L", T1L_SOP_DATE, dateHourInfo);
        testProject("T1LINT", T1LINT_SOP_DATE, dateHourInfo);
        testProject("T1NINT", T1NINT_SOP_DATE, dateHourInfo);
        testProject("T1P", T1P_SOP_DATE, dateHourInfo);
        testProject("T1P_INT", T1P_INT_SOP_DATE, dateHourInfo);
        
        // 验证MY1的两种算法结果是否一致
        System.out.println("=== 验证MY1算法一致性 ===");
        String my1DialPassword = generateDialPassword(MY1_SOP_DATE);
        System.out.println("MY1拨号密码（新算法）: " + formatPassword(my1DialPassword));
        System.out.println("算法统一: ✓ 所有项目现在都使用相同的拨号密码算法");
    }
    
    private static void testProject(String projectName, long sopDate, int[] dateHourInfo) {
        String sopPassword = generatePassword(dateHourInfo, sopDate);
        String dialPassword = generateDialPassword(sopDate);
        
        System.out.println(projectName + " (" + sopDate + "):");
        System.out.println("  SOP密码:  " + sopPassword);
        System.out.println("  拨号密码: " + formatPassword(dialPassword));
        System.out.println();
    }
    
    // 生成SOP密码
    private static String generatePassword(int[] dateHourInfo, long sopDate) {
        int monthDayHour = dateHourInfo[0];
        long result = sopDate * monthDayHour;
        return String.format("%06d", result % 1000000);
    }
    
    // 生成拨号密码
    private static String generateDialPassword(long sopDate) {
        Calendar now = Calendar.getInstance();
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        long data = (month * 10000) + (day * 100) + hour;
        long result = data * sopDate - hour;
        result = result % 1000000000;
        return String.format("%06d", result % 1000000);
    }
    
    // 格式化密码
    private static String formatPassword(String password) {
        return "*#" + password + "#*";
    }
    
    // 获取当前日期时间信息
    private static int[] getDateHourDigit() {
        Calendar now = Calendar.getInstance();
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        int monthDayHour = (month * 10000) + (day * 100) + hour;
        return new int[]{monthDayHour, hour};
    }
    
    // 获取当前时间字符串
    private static String getCurrentTimeString() {
        Calendar now = Calendar.getInstance();
        return String.format("%d年%d月%d日%d点", 
            now.get(Calendar.YEAR),
            now.get(Calendar.MONTH) + 1,
            now.get(Calendar.DAY_OF_MONTH),
            now.get(Calendar.HOUR_OF_DAY));
    }
}
