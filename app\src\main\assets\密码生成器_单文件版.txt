# MY1和T1J MY1密码生成器 - 单文件版Excel实现

这是最简单的实现方式，只需要一个Excel工作表，几个单元格就可以完成密码生成功能。

## 步骤1：创建新的Excel文件

打开Excel，创建一个新的空白工作表。

## 步骤2：设置基本布局

按照下表设置单元格内容：

| 单元格 | 内容 | 说明 |
|-------|------|------|
| A1 | MY1和T1J MY1密码生成器 | 主标题 |
| A3 | SOP日期： | 标签 |
| B3 | 20250110 | SOP日期常量 |
| A5 | MY1密码生成 | 子标题 |
| A6 | 当前密码： | 标签 |
| B6 | =RIGHT("000000"&MOD(B3*VALUE(TEXT(NOW(),"mm")&TEXT(NOW(),"dd")&TEXT(NOW(),"hh")),1000000),6) | MY1密码计算公式 |
| A8 | T1J MY1密码生成 | 子标题 |
| A9 | 当前密码： | 标签 |
| B9 | =RIGHT("000000"&MOD(MOD((MONTH(NOW())*10000+DAY(NOW())*100+HOUR(NOW()))*B3-HOUR(NOW()),1000000000),1000000),6) | T1J MY1密码计算公式 |
| A10 | 格式化密码： | 标签 |
| B10 | ="*#"&B9&"#*" | 添加前缀和后缀 |
| A12 | 按F9键刷新计算结果 | 使用说明 |

## 步骤3：格式化单元格

1. 选择B6、B9和B10单元格
2. 右键点击 -> 设置单元格格式 -> 数字 -> 文本
3. 这样可以确保显示前导零

## 步骤4：美化表格（可选）

1. 为标题设置更大的字体和醒目的颜色
2. 为密码结果设置不同的背景色，使其更易于辨认
3. 为整个表格添加边框

## 步骤5：添加刷新按钮（可选）

1. 插入 -> 形状 -> 选择一个按钮形状
2. 在按钮上添加文字"刷新密码"
3. 右键点击按钮 -> 分配宏
4. 创建一个新的宏，命名为"RefreshPasswords"
5. 在宏编辑器中添加以下代码：

```vba
Sub RefreshPasswords()
    Application.CalculateFull
End Sub
```

## 使用说明

1. 打开Excel文件后，密码会根据当前时间自动计算
2. 按F9键可以手动刷新计算结果
3. 如果添加了刷新按钮，点击按钮也可以刷新结果
4. 复制密码时，直接选择单元格B6（MY1密码）或B9/B10（T1J MY1密码）

## 公式详解

### MY1密码公式

```
=RIGHT("000000"&MOD(B3*VALUE(TEXT(NOW(),"mm")&TEXT(NOW(),"dd")&TEXT(NOW(),"hh")),1000000),6)
```

这个公式的工作原理：
1. `TEXT(NOW(),"mm")&TEXT(NOW(),"dd")&TEXT(NOW(),"hh")` - 获取当前月日时，格式为MMddHH
2. `VALUE(...)` - 将文本转换为数值
3. `B3*...` - 将SOP日期(20250110)乘以MMddHH
4. `MOD(...,1000000)` - 对结果取模1000000，得到6位或更少位数
5. `"000000"&...` - 在结果前添加6个零
6. `RIGHT(...,6)` - 取右边6位，确保结果是6位数字

### T1J MY1密码公式

```
=RIGHT("000000"&MOD(MOD((MONTH(NOW())*10000+DAY(NOW())*100+HOUR(NOW()))*B3-HOUR(NOW()),1000000000),1000000),6)
```

这个公式的工作原理：
1. `MONTH(NOW())*10000+DAY(NOW())*100+HOUR(NOW())` - 构造数据(M*10000+D*100+H)
2. `(...)*B3` - 乘以SOP日期(20250110)
3. `...-HOUR(NOW())` - 减去当前小时值
4. `MOD(...,1000000000)` - 对结果取模1000000000
5. `MOD(...,1000000)` - 再对结果取模1000000，得到6位或更少位数
6. `"000000"&...` - 在结果前添加6个零
7. `RIGHT(...,6)` - 取右边6位，确保结果是6位数字

## 注意事项

1. 这个实现使用NOW()函数获取当前日期和时间，每次按F9键时会更新
2. 如果需要自动定时刷新，需要使用VBA实现（参见完整版指南）
3. 为了更详细地显示计算过程，可以参考"简易版"指南中的分步骤实现 