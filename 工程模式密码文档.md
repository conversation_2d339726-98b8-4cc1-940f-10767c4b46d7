# 车机系统工程模式开启方法

## 打开方式概述

车机系统中的工程模式（`com.autolink.engineermode`）可以通过在拨号界面输入特定密码打开。根据不同车型，需要输入不同的密码，或者使用基于时间的动态密码。

## 固定密码列表

以下是各车型对应的工程模式固定密码：

| 车型 | 密码 |
|------|------|
| T1J | `*#20230830#*` |
| T1J MY1 | `20250110` (也支持动态密码) |

## 动态密码生成算法

对于 T1J MY1 车型，除了固定密码外，还支持基于时间的动态密码。算法如下：

```java
private final String getPassWordBySopDate(int sopDate) {
    LocalDateTime now = LocalDateTime.now();
    int monthValue = now.getMonthValue();    // 获取当前月份
    int dayOfMonth = now.getDayOfMonth();    // 获取当前日
    int hour = now.getHour();                // 获取当前小时
    
    // 计算公式：(月份*10000 + 日*100 + 小时) * sopDate
    long j = (monthValue * 10000) + (dayOfMonth * 100);
    long j2 = hour;
    long j3 = (j + j2) * sopDate;
    
    // 减去小时值
    long j4 = j3 - j2;
    
    // 对结果取模（1,000,000,000），并格式化为6位数字
    String format = String.format("%06d", Long.valueOf(j4 % 1000000000));
    
    return format;
}
```

### 算法步骤：

1. 获取当前日期和时间（月、日、小时）
2. 计算: `(月*10000 + 日*100 + 小时) * sopDate`
3. 从结果中减去小时值
4. 对结果取模 1,000,000,000
5. 将结果格式化为固定6位数字（不足位数用0填充）

### 动态密码输入格式

动态密码需要按以下格式输入：`*#[生成的6位数]#*`

## 示例计算

假设当前是 2023年7月15日14时，T1J MY1型号的 sopDate 值为 20250110：

1. 计算初始值: `(7*10000 + 15*100 + 14) = 71514`
2. 与 sopDate 相乘: `71514 * 20250110 = 1,448,152,367,940`
3. 减去小时值: `1,448,152,367,940 - 14 = 1,448,152,367,926`
4. 取模: `1,448,152,367,926 % 1,000,000,000 = 448,152,367,926 % 1,000,000,000 = 448,152,926`
5. 格式化为6位: 由于结果超过6位，取最后6位: `152926`

最终动态密码输入为: `*#152926#*`

## 源代码位置

相关实现可在 `sources/com/landmark/bluetooth/fragment/DialFragment.java` 文件中找到。 