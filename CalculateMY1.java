public class CalculateMY1 {
    
    // MY1项目的SOP日期
    private static final long MY1_SOP_DATE = 20250110L;
    
    public static void main(String[] args) {
        System.out.println("=== MY1 SOP密码计算 ===");
        System.out.println("时间: 2025年8月2日12点05分");
        System.out.println("项目: MY1 (T2-旅行者)");
        System.out.println("SOP日期: " + MY1_SOP_DATE);
        System.out.println();
        
        // 指定时间：2025年8月2日12点05分
        // 注意：算法中只使用到小时，不使用分钟
        int year = 2025;
        int month = 8;
        int day = 2;
        int hour = 12;
        int minute = 5; // 分钟在算法中不使用
        
        // 计算monthDayHour (MMddHH格式)
        int monthDayHour = (month * 10000) + (day * 100) + hour;
        
        System.out.println("计算过程:");
        System.out.println("月份: " + month);
        System.out.println("日期: " + day);
        System.out.println("小时: " + hour);
        System.out.println("分钟: " + minute + " (算法中不使用)");
        System.out.println();
        
        System.out.println("monthDayHour = " + month + " × 10000 + " + day + " × 100 + " + hour);
        System.out.println("monthDayHour = " + monthDayHour);
        System.out.println();
        
        // SOP密码算法: result = sopDate * monthDayHour; password = result % 1000000
        long result = MY1_SOP_DATE * monthDayHour;
        long sopPassword = result % 1000000;
        
        System.out.println("SOP密码算法:");
        System.out.println("result = SOP日期 × monthDayHour");
        System.out.println("result = " + MY1_SOP_DATE + " × " + monthDayHour);
        System.out.println("result = " + result);
        System.out.println();
        
        System.out.println("SOP密码 = result % 1000000");
        System.out.println("SOP密码 = " + result + " % 1000000");
        System.out.println("SOP密码 = " + sopPassword);
        System.out.println();
        
        // 格式化为6位数
        String formattedPassword = String.format("%06d", sopPassword);
        
        System.out.println("=== 最终结果 ===");
        System.out.println("MY1项目 SOP密码: " + formattedPassword);
        System.out.println();
        
        // 同时计算拨号密码
        System.out.println("=== 附加：拨号密码 ===");
        long dialData = monthDayHour;
        long dialResult = dialData * MY1_SOP_DATE - hour;
        dialResult = dialResult % 1000000000;
        long dialPassword = dialResult % 1000000;
        String formattedDialPassword = String.format("%06d", dialPassword);
        
        System.out.println("拨号密码算法:");
        System.out.println("result = (monthDayHour × SOP日期 - 小时) % 1000000000 % 1000000");
        System.out.println("result = (" + monthDayHour + " × " + MY1_SOP_DATE + " - " + hour + ") % 1000000000 % 1000000");
        System.out.println("拨号密码: *#" + formattedDialPassword + "#*");
        System.out.println();
        
        System.out.println("=== 总结 ===");
        System.out.println("时间: 2025年8月2日12点05分");
        System.out.println("MY1 SOP密码: " + formattedPassword);
        System.out.println("MY1 拨号密码: *#" + formattedDialPassword + "#*");
    }
}
