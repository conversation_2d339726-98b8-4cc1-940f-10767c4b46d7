public class SopCalculator {

    public static void main(String[] args) {
        // 已知条件：2025年8月2日12点01分
        int month = 8;
        int day = 2;
        int hour = 12;

        // 已知密码
        long dialPassword = 872908L;  // 拨号密码
        long sopPassword = 872920L;   // SOP密码

        System.out.println("=== 逆向计算SOP日期 ===");
        System.out.println("已知时间: 2025年8月2日12点01分");
        System.out.println("拨号密码: " + dialPassword);
        System.out.println("SOP密码: " + sopPassword);
        System.out.println();

        // 计算基础数据
        long monthDayHour = (month * 10000) + (day * 100) + hour; // 80212

        System.out.println("基础数据 monthDayHour: " + monthDayHour);
        System.out.println();
        
        // 逆向计算SOP密码的SOP日期
        System.out.println("=== 逆向计算SOP密码的SOP日期 ===");
        System.out.println("算法: result = sopDate * monthDayHour; password = result % 1000000");
        System.out.println("逆向: sopDate = (password + k * 1000000) / monthDayHour");
        System.out.println();

        for (int k = 0; k < 50; k++) {
            long numerator = sopPassword + (k * 1000000L);
            if (numerator % monthDayHour == 0) {
                long sopDate = numerator / monthDayHour;

                // 检查是否为合理的日期格式 (YYYYMMDD)
                if (sopDate >= 20000101L && sopDate <= 20991231L) {
                    System.out.println("找到合理的SOP日期: " + sopDate);

                    // 验证计算
                    long verification = (sopDate * monthDayHour) % 1000000;
                    System.out.println("验证: " + sopDate + " * " + monthDayHour + " % 1000000 = " + verification);
                    System.out.println();
                }
            }
        }
        
        // 逆向计算拨号密码的SOP日期
        System.out.println("=== 逆向计算拨号密码的SOP日期 ===");
        System.out.println("算法: result = data * sopDate - hour; password = (result % 1000000000) % 1000000");
        System.out.println("逆向: sopDate = (password + hour + k * 1000000000 + j * 1000000) / data");
        System.out.println();

        boolean found = false;
        for (int k = 0; k < 5 && !found; k++) {
            for (int j = 0; j < 100 && !found; j++) {
                long numerator = dialPassword + hour + (k * 1000000000L) + (j * 1000000L);
                if (numerator % monthDayHour == 0) {
                    long sopDate = numerator / monthDayHour;

                    // 检查是否为合理的日期格式 (YYYYMMDD)
                    if (sopDate >= 20000101L && sopDate <= 20991231L) {
                        System.out.println("找到合理的SOP日期: " + sopDate);

                        // 验证计算
                        long tempResult = monthDayHour * sopDate - hour;
                        long verification = (tempResult % 1000000000L) % 1000000L;
                        System.out.println("验证: ((" + monthDayHour + " * " + sopDate + " - " + hour + ") % 1000000000) % 1000000 = " + verification);
                        System.out.println();
                        found = true;
                    }
                }
            }
        }
        
        // 直接计算可能的SOP日期
        System.out.println("=== 直接计算分析 ===");

        // 对于SOP密码算法，尝试一些可能的值
        System.out.println("尝试计算SOP密码对应的SOP日期:");

        // 由于 872920 = (sopDate * 80212) % 1000000
        // 我们需要找到 sopDate，使得 sopDate * 80212 ≡ 872920 (mod 1000000)

        // 尝试一些2025年的日期
        for (int mm = 1; mm <= 12; mm++) {
            for (int dd = 1; dd <= 31; dd++) {
                long testDate = 20250000L + mm * 100 + dd;

                long sopResult = (testDate * monthDayHour) % 1000000;
                long dialResult1 = monthDayHour * testDate - hour;
                long dialResult2 = (dialResult1 % 1000000000L) % 1000000L;

                if (sopResult == sopPassword || dialResult2 == dialPassword) {
                    System.out.println("找到匹配的日期: " + testDate);
                    System.out.println("  SOP密码结果: " + sopResult + (sopResult == sopPassword ? " ✓" : ""));
                    System.out.println("  拨号密码结果: " + dialResult2 + (dialResult2 == dialPassword ? " ✓" : ""));
                    System.out.println();
                }
            }
        }
    }
}
