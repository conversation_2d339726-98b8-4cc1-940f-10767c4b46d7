<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>MY1和T1J MY1密码生成器 - 网页版</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .instructions {
            background-color: #f8f9fa;
            border-left: 4px solid #4caf50;
            padding: 10px 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .password-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .password-box {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            position: relative;
        }
        .password-box h3 {
            margin-top: 0;
            margin-bottom: 10px;
        }
        .password-value {
            font-family: monospace;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 2px;
            color: #333;
            text-align: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            position: relative;
            cursor: pointer;
            user-select: all;
        }
        .copy-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(255,255,255,0.8);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            color: #555;
        }
        .copy-btn:hover {
            background-color: #f1f1f1;
        }
        .my1-box {
            background-color: #e8f5e9;
            border-left: 5px solid #4caf50;
        }
        .t1j-box {
            background-color: #e3f2fd;
            border-left: 5px solid #2196f3;
        }
        .refresh-btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 12px 24px;
            text-align: center;
            text-decoration: none;
            display: block;
            font-size: 16px;
            margin: 30px auto 20px;
            cursor: pointer;
            border-radius: 4px;
            width: 200px;
            transition: background-color 0.3s;
        }
        .refresh-btn:hover {
            background-color: #45a049;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }
        .hidden {
            display: none;
        }
        .tooltip {
            position: absolute;
            background-color: #555;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
            white-space: nowrap;
            left: 50%;
            transform: translateX(-50%);
            bottom: -30px;
        }
        .tooltip.show {
            opacity: 1;
        }
        @media (max-width: 600px) {
            .password-value {
                font-size: 22px;
                letter-spacing: 1px;
            }
            .copy-btn {
                padding: 4px 8px;
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <h1>MY1和T1J MY1密码生成器</h1>
    
    <div class="password-container">
        <div class="instructions">
            <p>本页面显示当前的MY1项目密码和T1J MY1拨号密码，密码每小时更新。</p>
            <p>点击密码可以选择，点击"复制"按钮可以一键复制密码。</p>
        </div>

        <div class="password-box my1-box">
            <h3>MY1项目密码</h3>
            <div class="password-value" id="my1-password" onclick="selectText(this)">------</div>
            <button class="copy-btn" onclick="copyToClipboard('my1-password')">复制</button>
            <div class="tooltip" id="my1-tooltip">已复制到剪贴板</div>
        </div>

        <div class="password-box t1j-box">
            <h3>T1J MY1拨号密码</h3>
            <div class="password-value" id="t1j-password" onclick="selectText(this)">------</div>
            <button class="copy-btn" onclick="copyToClipboard('t1j-password')">复制</button>
            <div class="tooltip" id="t1j-tooltip">已复制到剪贴板</div>
        </div>

        <div class="password-box t1j-box">
            <h3>T1J MY1拨号密码（格式化）</h3>
            <div class="password-value" id="t1j-formatted" onclick="selectText(this)">*#------#*</div>
            <button class="copy-btn" onclick="copyToClipboard('t1j-formatted')">复制</button>
            <div class="tooltip" id="t1j-formatted-tooltip">已复制到剪贴板</div>
        </div>

        <button class="refresh-btn" onclick="refreshPasswords()">刷新密码</button>

        <div class="footer">
            密码基于当前时间生成，算法与Android应用完全一致。
        </div>
    </div>

    <!-- 隐藏的计算区域 -->
    <div class="hidden">
        <span id="sop-date">20250110</span>
        <span id="current-time"></span>
    </div>

    <script>
        // 计算密码
        function calculatePasswords() {
            const sopDate = parseInt(document.getElementById('sop-date').textContent);
            const now = new Date();
            
            // 更新当前时间
            document.getElementById('current-time').textContent = now.toString();
            
            // 计算MY1密码
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hour = String(now.getHours()).padStart(2, '0');
            const mmddhhValue = parseInt(month + day + hour);
            
            const my1Result = (sopDate * mmddhhValue) % 1000000;
            const my1Password = String(my1Result).padStart(6, '0');
            
            // 计算T1J MY1密码
            const m = now.getMonth() + 1;
            const d = now.getDate();
            const h = now.getHours();
            
            const data = (m * 10000) + (d * 100) + h;
            let result = data * sopDate;
            result = result - h;
            result = result % 1000000000;
            result = result % 1000000;
            
            const t1jPassword = String(result).padStart(6, '0');
            const t1jFormatted = "*#" + t1jPassword + "#*";
            
            // 更新显示
            document.getElementById('my1-password').textContent = my1Password;
            document.getElementById('t1j-password').textContent = t1jPassword;
            document.getElementById('t1j-formatted').textContent = t1jFormatted;
        }
        
        // 复制到剪贴板
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            // 创建临时textarea
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                document.execCommand('copy');
                showTooltip(elementId + '-tooltip');
            } catch (err) {
                console.error('复制失败:', err);
            }
            
            document.body.removeChild(textarea);
        }
        
        // 显示提示
        function showTooltip(tooltipId) {
            const tooltip = document.getElementById(tooltipId);
            tooltip.classList.add('show');
            
            setTimeout(() => {
                tooltip.classList.remove('show');
            }, 2000);
        }
        
        // 选择文本
        function selectText(element) {
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }
        
        // 刷新密码
        function refreshPasswords() {
            calculatePasswords();
        }
        
        // 页面加载时计算密码
        window.onload = function() {
            calculatePasswords();
            
            // 每小时自动刷新一次
            setInterval(calculatePasswords, 60 * 60 * 1000);
        };
    </script>
</body>
</html> 