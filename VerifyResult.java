public class VerifyResult {
    
    public static void main(String[] args) {
        // 逆向计算得到的SOP日期
        long sopDate = 20240910L;
        
        // 已知条件：2025年8月2日12点01分
        int month = 8;
        int day = 2;
        int hour = 12;
        
        // 计算基础数据
        long monthDayHour = (month * 10000) + (day * 100) + hour; // 80212
        
        System.out.println("=== 验证逆向计算结果 ===");
        System.out.println("计算得到的SOP日期: " + sopDate + " (2024年9月10日)");
        System.out.println("验证时间: 2025年8月2日12点");
        System.out.println("monthDayHour: " + monthDayHour);
        System.out.println();
        
        // 验证SOP密码算法
        System.out.println("=== 验证SOP密码算法 ===");
        System.out.println("算法: result = sopDate * monthDayHour; password = result % 1000000");
        
        long sopResult = sopDate * monthDayHour;
        long sopPassword = sopResult % 1000000;
        
        System.out.println("计算过程:");
        System.out.println("  " + sopDate + " * " + monthDayHour + " = " + sopResult);
        System.out.println("  " + sopResult + " % 1000000 = " + sopPassword);
        System.out.println("结果: SOP密码 = " + sopPassword);
        System.out.println("预期: 872920");
        System.out.println("匹配: " + (sopPassword == 872920L ? "✓ 正确" : "✗ 错误"));
        System.out.println();
        
        // 验证拨号密码算法
        System.out.println("=== 验证拨号密码算法 ===");
        System.out.println("算法: result = data * sopDate - hour; password = (result % 1000000000) % 1000000");
        
        long dialTempResult = monthDayHour * sopDate - hour;
        long dialResult1 = dialTempResult % 1000000000L;
        long dialPassword = dialResult1 % 1000000L;
        
        System.out.println("计算过程:");
        System.out.println("  " + monthDayHour + " * " + sopDate + " - " + hour + " = " + dialTempResult);
        System.out.println("  " + dialTempResult + " % 1000000000 = " + dialResult1);
        System.out.println("  " + dialResult1 + " % 1000000 = " + dialPassword);
        System.out.println("结果: 拨号密码 = " + dialPassword);
        System.out.println("预期: 872908");
        System.out.println("匹配: " + (dialPassword == 872908L ? "✓ 正确" : "✗ 错误"));
        System.out.println();
        
        // 总结
        System.out.println("=== 总结 ===");
        System.out.println("逆向计算成功!");
        System.out.println("SOP日期: " + sopDate + " (2024年9月10日)");
        System.out.println("该SOP日期在2025年8月2日12点时:");
        System.out.println("  - 生成的SOP密码: " + sopPassword + " ✓");
        System.out.println("  - 生成的拨号密码: " + dialPassword + " ✓");
    }
}
