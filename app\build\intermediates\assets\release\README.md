# MY1和T1J MY1密码生成器Excel实现指南

本文档提供了将Android应用中的密码生成算法移植到Excel的多种实现方案。根据您的需求和Excel使用经验，可以选择最适合的方案。

## 实现方案概述

本文档包含三种实现方案：

1. **单文件版** - 最简单的实现，一个工作表包含所有功能，适合大多数用户
2. **简易版** - 使用多个工作表和纯Excel公式，展示详细的计算过程
3. **完整版** - 使用VBA实现自动刷新功能，最接近原始Android应用的体验

## 文件说明

- `密码生成器_单文件版.txt` - 单文件版实现指南
- `密码生成器_简易版.txt` - 简易版实现指南
- `密码生成器_完整版.txt` - 完整版实现指南（包含VBA代码）
- `密码生成器.xlsx` - 简要实现指南

## 选择合适的实现方案

- 如果您只需要快速查看密码，推荐使用**单文件版**
- 如果您想了解密码生成的详细过程，推荐使用**简易版**
- 如果您需要与Android应用相同的自动刷新功能，推荐使用**完整版**

## 密码算法说明

这些Excel实现基于Android应用中的两个密码生成算法：

### MY1密码算法

```java
// 原始Java代码
private String generatePassword(int[] dateHourInfo, long sopDate) {
    int monthDayHour = dateHourInfo[0]; // MMddHH格式的数字
    long result = sopDate * monthDayHour;
    return String.format("%06d", result % 1000000);
}
```

### T1J MY1密码算法

```java
// 原始Java代码
private String generateT1jMy1Password() {
    Calendar now = Calendar.getInstance();
    int month = now.get(Calendar.MONTH) + 1; 
    int day = now.get(Calendar.DAY_OF_MONTH);
    int hour = now.get(Calendar.HOUR_OF_DAY);
    
    long data = (month * 10000) + (day * 100) + hour; 
    long result = data * MY1_SOP_DATE;
    result = result - hour;
    result = result % 1000000000;
    
    String password = String.format("%06d", result % 1000000);
    return password;
}
```

## 常见问题解答

### 密码不正确或格式错误？

1. 确保SOP日期设置为20250110
2. 检查日期和时间格式是否正确
3. 确保密码单元格设置为文本格式，以显示前导零
4. 按F9键手动刷新计算结果

### 如何实现自动刷新？

1. 使用完整版中的VBA代码
2. 确保Excel安全设置允许运行宏
3. 打开文件时会自动开始刷新

### Excel和Android应用的结果不一致？

1. 检查时间是否同步
2. 确保使用相同的SOP日期常量
3. 检查Excel中的公式是否正确实现了Java代码的逻辑

## 联系方式

如有任何问题或需要进一步的帮助，请联系应用开发团队。 