{"logs": [{"outputFile": "com.autolink.any.app-mergeReleaseResources-29:/values-v24/values-v24.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c040f7fd3bcdbcca32f80023251103f6\\transformed\\appcompat-1.7.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dc8b32dcc042d5269c0b3f891bacfc7e\\transformed\\material-1.12.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5,6,9,12,15", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,140,239,326,427,626,839,1040", "endLines": "2,3,4,5,8,11,14,17", "endColumns": "84,98,86,100,10,10,10,10", "endOffsets": "135,234,321,422,621,834,1035,1250"}, "to": {"startLines": "4,5,6,7,8,11,14,17", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "347,432,531,618,719,918,1131,1332", "endLines": "4,5,6,7,10,13,16,19", "endColumns": "84,98,86,100,10,10,10,10", "endOffsets": "427,526,613,714,913,1126,1327,1542"}}]}]}