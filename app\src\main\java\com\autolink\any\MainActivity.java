package com.autolink.any;

import androidx.appcompat.app.AppCompatActivity;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class MainActivity extends AppCompatActivity {
    private final String LOG_TAG = "MainActivity";
    private TextView my1ModeText;
    private TextView t1jMy1ModeText;
    private Button copyMy1Button;
    private Button copyT1jMy1Button;

    // 新增的UI控件
    private TextView t1lModeText;
    private TextView t1lintModeText;
    private TextView t1nintModeText;
    private TextView t1pModeText;
    private TextView t1pIntModeText;
    private Button copyT1lButton;
    private Button copyT1lintButton;
    private Button copyT1nintButton;
    private Button copyT1pButton;
    private Button copyT1pIntButton;

    private Handler handler;
    private final int REFRESH_INTERVAL = 10000; // 10秒 = 10000毫秒

    // SOP日期常量，来自文档
    private static final long MY1_SOP_DATE = 20250110L;
    private static final long T1L_SOP_DATE = 20248910L;
    private static final long T1LINT_SOP_DATE = 20241130L;
    private static final long T1NINT_SOP_DATE = 20240830L;
    private static final long T1P_SOP_DATE = 20231030L;
    private static final long T1P_INT_SOP_DATE = 20240530L;
    
    private final Runnable refreshRunnable = new Runnable() {
        @Override
        public void run() {
            updateDisplayInfo();
            // 继续循环执行
            handler.postDelayed(this, REFRESH_INTERVAL);
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化控件
        my1ModeText = findViewById(R.id.my1_mode_text);
        t1jMy1ModeText = findViewById(R.id.t1j_my1_mode_text);
        copyMy1Button = findViewById(R.id.copy_my1_button);
        copyT1jMy1Button = findViewById(R.id.copy_t1j_my1_button);

        // 初始化新增的控件
        t1lModeText = findViewById(R.id.t1l_mode_text);
        t1lintModeText = findViewById(R.id.t1lint_mode_text);
        t1nintModeText = findViewById(R.id.t1nint_mode_text);
        t1pModeText = findViewById(R.id.t1p_mode_text);
        t1pIntModeText = findViewById(R.id.t1p_int_mode_text);
        copyT1lButton = findViewById(R.id.copy_t1l_button);
        copyT1lintButton = findViewById(R.id.copy_t1lint_button);
        copyT1nintButton = findViewById(R.id.copy_t1nint_button);
        copyT1pButton = findViewById(R.id.copy_t1p_button);
        copyT1pIntButton = findViewById(R.id.copy_t1p_int_button);
        
        // 设置复制按钮点击事件
        copyMy1Button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(my1ModeText.getText().toString());
            }
        });
        
        copyT1jMy1Button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(t1jMy1ModeText.getText().toString());
            }
        });

        // 设置新增按钮的点击事件
        copyT1lButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(t1lModeText.getText().toString());
            }
        });

        copyT1lintButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(t1lintModeText.getText().toString());
            }
        });

        copyT1nintButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(t1nintModeText.getText().toString());
            }
        });

        copyT1pButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(t1pModeText.getText().toString());
            }
        });

        copyT1pIntButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(t1pIntModeText.getText().toString());
            }
        });
        
        // 初始化Handler
        handler = new Handler(Looper.getMainLooper());
        
        // 首次更新显示
        updateDisplayInfo();
        
        // 开始定时刷新
        handler.postDelayed(refreshRunnable, REFRESH_INTERVAL);
    }
    
    // 复制到剪贴板
    private void copyToClipboard(String textToCopy) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("工厂模式码", textToCopy);
        clipboard.setPrimaryClip(clip);
        Toast.makeText(this, "已复制: " + textToCopy, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除回调，防止内存泄漏
        if (handler != null) {
            handler.removeCallbacks(refreshRunnable);
        }
    }
    
    // 更新显示信息
    private void updateDisplayInfo() {
        // 获取日期和小时信息
        int[] dateHourInfo = getDateHourDigit();
        
        // 显示MY1项目SOP密码 - 不添加前缀和后缀
        String my1Result = generatePassword(dateHourInfo, MY1_SOP_DATE);
        my1ModeText.setText(my1Result);
        
        // 显示T1J MY1拨号密码 - 保留前缀和后缀
        String t1jMy1Result = generateT1jMy1Password();
        t1jMy1ModeText.setText(formatPassword(t1jMy1Result));
        
        // 记录日志
        Log.d(LOG_TAG, "显示信息已更新: " + new Date().toString());
    }
    
    private final int[] getDateHourDigit() {
        int i;
        int i2;
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MMddHH");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("HH");
        try {
            String format = simpleDateFormat.format(date);
            // Intrinsics.checkNotNullExpressionValue(format, "dateHourFormat.format(date)");
            i2 = Integer.parseInt(format);
            String format2 = simpleDateFormat2.format(date);
            // Intrinsics.checkNotNullExpressionValue(format2, "hourFormat.format(date)");
            i = Integer.parseInt(format2);
        } catch (NumberFormatException unused) {
            i = 0;
            i2 = 0;
        }
        return new int[]{i2, i};
    }
    
    // 按照文档进行密码生成
    private String generatePassword(int[] dateHourInfo, long sopDate) {
        int monthDayHour = dateHourInfo[0]; // MMddHH格式的数字
        
        // 按照文档实现密码生成算法
        long result = sopDate * monthDayHour;
        
        // 获取6位数密码
        return String.format("%06d", result % 1000000);
    }
    
    // 格式化密码，添加前缀和后缀
    private String formatPassword(String password) {
        return "*#" + password + "#*";
    }
    
    // 生成T1J MY1拨号密码
    private String generateT1jMy1Password() {
        Calendar now = Calendar.getInstance();
        int month = now.get(Calendar.MONTH) + 1; // 月份从0开始，需要+1
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        // 按照T1J MY1的密码生成规则
        long data = (month * 10000) + (day * 100) + hour; 
        long result = data * MY1_SOP_DATE; // MY1的SOP日期
        
        // 从结果中减去小时值
        result = result - hour;
        
        // 对结果取模
        result = result % 1000000000;
        
        // 格式化为6位数
        String password = String.format("%06d", result % 1000000);
        
        return password;
    }
}