<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>MY1和T1J MY1密码生成器 - Excel生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .instructions {
            background-color: #f8f9fa;
            border-left: 4px solid #4caf50;
            padding: 10px 20px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .formula {
            font-family: Consolas, monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
        }
        .copy-btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #ccc;
        }
        .tabcontent {
            display: none;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-top: none;
        }
        #default {
            display: block;
        }
    </style>
</head>
<body>
    <h1>MY1和T1J MY1密码生成器 - Excel生成器</h1>
    
    <div class="instructions">
        <h3>使用说明</h3>
        <p>1. 选择下方您想要的实现版本</p>
        <p>2. 点击"复制到剪贴板"按钮</p>
        <p>3. 打开Excel，选择A1单元格，粘贴内容</p>
        <p>4. Excel会自动创建表格和公式</p>
        <p>5. 按F9键刷新计算结果</p>
    </div>

    <div class="tab">
        <button class="tablinks active" onclick="openTab(event, 'default')">单文件版（最简单）</button>
        <button class="tablinks" onclick="openTab(event, 'detailed')">详细版</button>
        <button class="tablinks" onclick="openTab(event, 'advanced')">高级版说明</button>
    </div>

    <div id="default" class="tabcontent">
        <h2>单文件版 - 最简单实现</h2>
        <p>这是最简单的实现方式，只需要一个工作表和几个单元格。</p>
        <button class="copy-btn" onclick="copyToClipboard('single-table')">复制到剪贴板</button>
        <table id="single-table">
            <tr>
                <td>MY1和T1J MY1密码生成器</td>
                <td></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>SOP日期：</td>
                <td>20250110</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>MY1密码生成</td>
                <td></td>
            </tr>
            <tr>
                <td>当前密码：</td>
                <td>=RIGHT("000000"&MOD(B3*VALUE(TEXT(NOW(),"mm")&TEXT(NOW(),"dd")&TEXT(NOW(),"hh")),1000000),6)</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>T1J MY1密码生成</td>
                <td></td>
            </tr>
            <tr>
                <td>当前密码：</td>
                <td>=RIGHT("000000"&MOD(MOD((MONTH(NOW())*10000+DAY(NOW())*100+HOUR(NOW()))*B3-HOUR(NOW()),1000000000),1000000),6)</td>
            </tr>
            <tr>
                <td>格式化密码：</td>
                <td>="*#"&B9&"#*"</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>按F9键刷新计算结果</td>
                <td></td>
            </tr>
        </table>
    </div>

    <div id="detailed" class="tabcontent">
        <h2>详细版 - 显示计算过程</h2>
        <p>这个版本会显示详细的计算过程，便于理解算法原理。</p>
        
        <h3>工作表1: MY1密码</h3>
        <button class="copy-btn" onclick="copyToClipboard('my1-table')">复制MY1工作表到剪贴板</button>
        <table id="my1-table">
            <tr>
                <td>MY1项目SOP密码生成器</td>
                <td></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>SOP日期：</td>
                <td>20250110</td>
            </tr>
            <tr>
                <td>当前日期：</td>
                <td>=TODAY()</td>
            </tr>
            <tr>
                <td>当前时间：</td>
                <td>=NOW()</td>
            </tr>
            <tr>
                <td>月日时(MMddHH)：</td>
                <td>=VALUE(TEXT(B4,"mm")&TEXT(B4,"dd")&TEXT(B5,"hh"))</td>
            </tr>
            <tr>
                <td>计算结果：</td>
                <td>=B3*B6</td>
            </tr>
            <tr>
                <td>MY1密码：</td>
                <td>=RIGHT("000000"&MOD(B7,1000000),6)</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>注意：按F9可手动刷新计算结果</td>
                <td></td>
            </tr>
        </table>

        <h3>工作表2: T1J_MY1密码</h3>
        <button class="copy-btn" onclick="copyToClipboard('t1j-table')">复制T1J工作表到剪贴板</button>
        <table id="t1j-table">
            <tr>
                <td>T1J MY1拨号密码生成器</td>
                <td></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>SOP日期：</td>
                <td>20250110</td>
            </tr>
            <tr>
                <td>当前日期：</td>
                <td>=TODAY()</td>
            </tr>
            <tr>
                <td>当前时间：</td>
                <td>=NOW()</td>
            </tr>
            <tr>
                <td>月份：</td>
                <td>=MONTH(B4)</td>
            </tr>
            <tr>
                <td>日期：</td>
                <td>=DAY(B4)</td>
            </tr>
            <tr>
                <td>小时：</td>
                <td>=HOUR(B5)</td>
            </tr>
            <tr>
                <td>构造数据：</td>
                <td>=B6*10000+B7*100+B8</td>
            </tr>
            <tr>
                <td>计算结果1：</td>
                <td>=B9*B3</td>
            </tr>
            <tr>
                <td>减去小时值：</td>
                <td>=B10-B8</td>
            </tr>
            <tr>
                <td>取模结果1：</td>
                <td>=MOD(B11,1000000000)</td>
            </tr>
            <tr>
                <td>取模结果2：</td>
                <td>=MOD(B12,1000000)</td>
            </tr>
            <tr>
                <td>T1J MY1密码：</td>
                <td>=RIGHT("000000"&B13,6)</td>
            </tr>
            <tr>
                <td>格式化密码：</td>
                <td>="*#"&B14&"#*"</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>注意：按F9可手动刷新计算结果</td>
                <td></td>
            </tr>
        </table>
    </div>

    <div id="advanced" class="tabcontent">
        <h2>高级版 - 自动刷新功能</h2>
        <p>这个版本使用VBA实现自动刷新功能，最接近原始Android应用的体验。</p>
        <p>由于VBA代码较长，请参考"密码生成器_完整版.txt"文件中的实现说明。</p>
        
        <div class="instructions">
            <h3>实现步骤</h3>
            <ol>
                <li>创建两个工作表，分别命名为"MY1密码"和"T1J_MY1密码"</li>
                <li>按照详细版的表格设置基本布局</li>
                <li>按Alt+F11打开VBA编辑器</li>
                <li>插入一个新模块，复制"密码生成器_完整版.txt"中的VBA代码</li>
                <li>在ThisWorkbook模块中添加自动启动代码</li>
                <li>保存为启用宏的Excel文件(.xlsm)</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            // 创建一个隐藏的textarea
            var textarea = document.createElement('textarea');
            
            // 获取表格内容并转换为制表符分隔的文本
            var table = document.getElementById(elementId);
            var text = '';
            for (var i = 0; i < table.rows.length; i++) {
                var row = '';
                for (var j = 0; j < table.rows[i].cells.length; j++) {
                    row += table.rows[i].cells[j].innerText + '\t';
                }
                text += row + '\n';
            }
            
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                var successful = document.execCommand('copy');
                var msg = successful ? '成功复制到剪贴板' : '复制失败';
                alert(msg);
            } catch (err) {
                alert('复制失败: ' + err);
            }
            
            document.body.removeChild(textarea);
        }
        
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html> 