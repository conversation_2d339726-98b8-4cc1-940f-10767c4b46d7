import java.util.Calendar;

public class TestDialPasswords {
    
    // SOP日期常量
    private static final long MY1_SOP_DATE = 20250110L;
    private static final long T1L_SOP_DATE = 20240910L;
    private static final long T1LINT_SOP_DATE = 20241130L;
    private static final long T1NINT_SOP_DATE = 20240830L;
    private static final long T1P_SOP_DATE = 20231030L;
    private static final long T1P_INT_SOP_DATE = 20240530L;
    
    public static void main(String[] args) {
        System.out.println("=== 测试拨号密码功能 ===");
        System.out.println("当前时间: " + getCurrentTimeString());
        System.out.println();
        
        // 测试所有项目的拨号密码
        System.out.println("各项目拨号密码:");
        System.out.println("MY1:     " + formatPassword(generateDialPassword(MY1_SOP_DATE)));
        System.out.println("T1L:     " + formatPassword(generateDialPassword(T1L_SOP_DATE)));
        System.out.println("T1LINT:  " + formatPassword(generateDialPassword(T1LINT_SOP_DATE)));
        System.out.println("T1NINT:  " + formatPassword(generateDialPassword(T1NINT_SOP_DATE)));
        System.out.println("T1P:     " + formatPassword(generateDialPassword(T1P_SOP_DATE)));
        System.out.println("T1P_INT: " + formatPassword(generateDialPassword(T1P_INT_SOP_DATE)));
        System.out.println();
        
        // 测试SOP密码
        int[] dateHourInfo = getDateHourDigit();
        System.out.println("各项目SOP密码:");
        System.out.println("MY1:     " + generatePassword(dateHourInfo, MY1_SOP_DATE));
        System.out.println("T1L:     " + generatePassword(dateHourInfo, T1L_SOP_DATE));
        System.out.println("T1LINT:  " + generatePassword(dateHourInfo, T1LINT_SOP_DATE));
        System.out.println("T1NINT:  " + generatePassword(dateHourInfo, T1NINT_SOP_DATE));
        System.out.println("T1P:     " + generatePassword(dateHourInfo, T1P_SOP_DATE));
        System.out.println("T1P_INT: " + generatePassword(dateHourInfo, T1P_INT_SOP_DATE));
        System.out.println();
        
        // 验证T1L的特殊情况（2025年8月2日12点）
        System.out.println("=== 验证T1L在2025年8月2日12点的密码 ===");
        int[] testDateHour = {80212, 12}; // 2025年8月2日12点
        String t1lSopPassword = generatePassword(testDateHour, T1L_SOP_DATE);
        String t1lDialPassword = generateDialPasswordForTime(T1L_SOP_DATE, 8, 2, 12);
        
        System.out.println("T1L SOP密码: " + t1lSopPassword);
        System.out.println("T1L 拨号密码: " + formatPassword(t1lDialPassword));
        System.out.println("预期SOP密码: 872920");
        System.out.println("预期拨号密码: *#872908#*");
        System.out.println("SOP密码匹配: " + (t1lSopPassword.equals("872920") ? "✓" : "✗"));
        System.out.println("拨号密码匹配: " + (formatPassword(t1lDialPassword).equals("*#872908#*") ? "✓" : "✗"));
    }
    
    // 生成拨号密码
    private static String generateDialPassword(long sopDate) {
        Calendar now = Calendar.getInstance();
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        return generateDialPasswordForTime(sopDate, month, day, hour);
    }
    
    // 为指定时间生成拨号密码
    private static String generateDialPasswordForTime(long sopDate, int month, int day, int hour) {
        long data = (month * 10000) + (day * 100) + hour;
        long result = data * sopDate - hour;
        result = result % 1000000000;
        return String.format("%06d", result % 1000000);
    }
    
    // 生成SOP密码
    private static String generatePassword(int[] dateHourInfo, long sopDate) {
        int monthDayHour = dateHourInfo[0];
        long result = sopDate * monthDayHour;
        return String.format("%06d", result % 1000000);
    }
    
    // 格式化密码
    private static String formatPassword(String password) {
        return "*#" + password + "#*";
    }
    
    // 获取当前日期时间信息
    private static int[] getDateHourDigit() {
        Calendar now = Calendar.getInstance();
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        int monthDayHour = (month * 10000) + (day * 100) + hour;
        return new int[]{monthDayHour, hour};
    }
    
    // 获取当前时间字符串
    private static String getCurrentTimeString() {
        Calendar now = Calendar.getInstance();
        return String.format("%d年%d月%d日%d点", 
            now.get(Calendar.YEAR),
            now.get(Calendar.MONTH) + 1,
            now.get(Calendar.DAY_OF_MONTH),
            now.get(Calendar.HOUR_OF_DAY));
    }
}
