# MY1和T1J MY1密码生成器使用说明

## 文件说明

本应用提供了多种格式的密码生成器文件，您可以根据需要选择合适的版本：

1. **密码生成器.xml** - 直接显示两个密码的美化版Excel文件（推荐）
2. **密码生成器_直接显示版.csv** - 简洁版CSV格式，可直接在Excel中打开
3. **密码生成器_MY1密码.csv** 和 **密码生成器_T1J密码.csv** - 详细版CSV格式
4. **密码生成器_美化版.html** - 可在浏览器中打开，并复制到Excel的HTML页面
5. **excel_generator.html** - 多版本Excel生成器HTML工具

## 使用方法

### 方法一：使用XML格式（推荐）

1. 将**密码生成器.xml**文件复制到您的电脑
2. 双击打开文件，它会自动在Excel中打开
3. 如果提示，请选择"是"允许打开
4. 按F9键刷新计算结果，显示当前密码
5. 使用"另存为"功能保存为.xlsx或.xls格式

### 方法二：使用CSV格式

1. 将任意CSV文件复制到您的电脑
2. 双击打开文件，它会自动在Excel中打开
3. 如果Excel中显示的公式有错误，请检查分隔符是否正确
   - 可能需要将分号(;)替换为逗号(,)，或反之
4. 按F9键刷新计算结果，显示当前密码
5. 使用"另存为"功能保存为.xlsx或.xls格式

### 方法三：使用HTML工具

1. 将**密码生成器_美化版.html**或**excel_generator.html**复制到您的电脑
2. 使用浏览器打开HTML文件
3. 点击"复制到剪贴板"按钮
4. 打开Excel，选择A1单元格
5. 粘贴内容
6. 按F9键刷新计算结果，显示当前密码

## 注意事项

1. 所有版本的密码生成器都使用相同的算法，与Android应用完全一致
2. 密码会根据当前日期和时间自动计算
3. 需要手动按F9键刷新计算结果
4. 如需自动刷新功能，请参考"密码生成器_完整版.txt"中的VBA实现说明

## 密码说明

1. **MY1项目密码** - 用于加密项目的密码
2. **T1J MY1拨号密码** - 用于拨号的密码，格式为 *#密码#*

## 常见问题

### 公式显示错误？

- 检查Excel中的公式分隔符设置（逗号或分号）
- 确保单元格格式设置为"文本"，以显示前导零

### 密码不正确？

- 确保SOP日期设置为20250110
- 检查电脑系统时间是否正确
- 按F9键手动刷新计算结果

### 如何实现自动刷新？

请参考"密码生成器_完整版.txt"中的VBA实现说明，创建启用宏的Excel文件。 