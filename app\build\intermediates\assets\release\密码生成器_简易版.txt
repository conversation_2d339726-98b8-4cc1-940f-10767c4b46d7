# MY1和T1J MY1密码生成器 - 简易Excel实现指南

这个指南提供了一种简单的方法，使用纯Excel公式（不需要VBA）来实现密码生成算法。这种方法适合不熟悉VBA或在受限环境中使用Excel的用户。

## 创建工作表

1. 打开一个新的Excel文件
2. 创建两个工作表，分别命名为"MY1密码"和"T1J_MY1密码"

## 工作表1: MY1密码

### 设置单元格内容

| 单元格 | 内容 | 说明 |
|-------|------|------|
| A1 | MY1项目SOP密码生成器 | 标题 |
| A3 | SOP日期： | 标签 |
| B3 | 20250110 | SOP日期常量 |
| A4 | 当前日期： | 标签 |
| B4 | =TODAY() | 当前日期函数 |
| A5 | 当前时间： | 标签 |
| B5 | =NOW() | 当前日期时间函数 |
| A6 | 月日时(MMddHH)： | 标签 |
| B6 | =VALUE(TEXT(B4,"mm")&TEXT(B4,"dd")&TEXT(B5,"hh")) | 计算MMddHH格式数值 |
| A7 | 计算结果： | 标签 |
| B7 | =B3*B6 | SOP日期乘以MMddHH |
| A8 | MY1密码： | 标签 |
| B8 | =RIGHT("000000"&MOD(B7,1000000),6) | 取模并格式化为6位数字 |
| A10 | 注意：按F9可手动刷新计算结果 | 使用说明 |

### 格式设置

1. 将B8单元格设置为"文本"格式，确保显示前导零
2. 可以为标题和密码结果设置醒目的字体和颜色

## 工作表2: T1J_MY1密码

### 设置单元格内容

| 单元格 | 内容 | 说明 |
|-------|------|------|
| A1 | T1J MY1拨号密码生成器 | 标题 |
| A3 | SOP日期： | 标签 |
| B3 | 20250110 | SOP日期常量 |
| A4 | 当前日期： | 标签 |
| B4 | =TODAY() | 当前日期函数 |
| A5 | 当前时间： | 标签 |
| B5 | =NOW() | 当前日期时间函数 |
| A6 | 月份： | 标签 |
| B6 | =MONTH(B4) | 提取月份 |
| A7 | 日期： | 标签 |
| B7 | =DAY(B4) | 提取日期 |
| A8 | 小时： | 标签 |
| B8 | =HOUR(B5) | 提取小时 |
| A9 | 构造数据： | 标签 |
| B9 | =B6*10000+B7*100+B8 | 构造数据(M*10000+D*100+H) |
| A10 | 计算结果1： | 标签 |
| B10 | =B9*B3 | 构造数据乘以SOP日期 |
| A11 | 计算结果2： | 标签 |
| B11 | =B10-B8 | 减去小时值 |
| A12 | 计算结果3： | 标签 |
| B12 | =MOD(B11,1000000000) | 对结果取模1000000000 |
| A13 | 计算结果4： | 标签 |
| B13 | =MOD(B12,1000000) | 对结果取模1000000 |
| A14 | T1J MY1密码： | 标签 |
| B14 | =RIGHT("000000"&B13,6) | 格式化为6位数字 |
| A15 | 格式化密码： | 标签 |
| B15 | ="*#"&B14&"#*" | 添加前缀和后缀 |
| A17 | 注意：按F9可手动刷新计算结果 | 使用说明 |

### 格式设置

1. 将B14和B15单元格设置为"文本"格式，确保显示前导零
2. 可以为标题和密码结果设置醒目的字体和颜色

## 使用说明

1. 打开Excel文件后，日期和时间会自动更新
2. 按F9键可以手动刷新计算结果
3. 密码会根据当前日期和时间自动计算
4. 如果需要自动刷新，可以参考"完整版"指南中的VBA实现

## 简化版（单工作表）

如果希望将所有内容放在一个工作表中，可以使用以下布局：

| 单元格 | 内容 | 说明 |
|-------|------|------|
| A1 | 密码生成器 | 标题 |
| A3 | SOP日期： | 标签 |
| B3 | 20250110 | SOP日期常量 |
| A4 | 当前日期时间： | 标签 |
| B4 | =NOW() | 当前日期时间函数 |
| A6 | MY1密码： | 标签 |
| B6 | =RIGHT("000000"&MOD(B3*VALUE(TEXT(B4,"mm")&TEXT(B4,"dd")&TEXT(B4,"hh")),1000000),6) | MY1密码计算公式 |
| A8 | T1J MY1密码： | 标签 |
| B8 | =RIGHT("000000"&MOD(MOD((MONTH(B4)*10000+DAY(B4)*100+HOUR(B4))*B3-HOUR(B4),1000000000),1000000),6) | T1J MY1密码计算公式 |
| A9 | 格式化T1J密码： | 标签 |
| B9 | ="*#"&B8&"#*" | 添加前缀和后缀 |
| A11 | 注意：按F9可手动刷新计算结果 | 使用说明 |

## 注意事项

1. Excel的日期和时间函数可能与实际设备时间有微小差异
2. 如果密码显示不正确，检查以下几点：
   - 确保日期和时间格式设置正确
   - 检查公式中的文本格式化函数是否正确
   - 确保数值计算中没有精度问题
3. 为了更准确的结果，可以考虑使用VBA实现（参见完整版指南） 