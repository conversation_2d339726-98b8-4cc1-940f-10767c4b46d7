public class PreciseCalculator {
    
    public static void main(String[] args) {
        // 已知条件：2025年8月2日12点01分
        int month = 8;
        int day = 2;
        int hour = 12;
        
        // 已知密码
        long dialPassword = 872908L;  // 拨号密码
        long sopPassword = 872920L;   // SOP密码
        
        // 计算基础数据
        long monthDayHour = (month * 10000) + (day * 100) + hour; // 80212
        
        System.out.println("=== 精确逆向计算 ===");
        System.out.println("时间: 2025年8月2日12点");
        System.out.println("monthDayHour: " + monthDayHour);
        System.out.println("已知SOP密码: " + sopPassword);
        System.out.println("已知拨号密码: " + dialPassword);
        System.out.println();
        
        // 方法1：暴力搜索合理的SOP日期范围
        System.out.println("=== 方法1：搜索2020-2030年的所有日期 ===");
        
        for (int year = 2020; year <= 2030; year++) {
            for (int mm = 1; mm <= 12; mm++) {
                for (int dd = 1; dd <= 31; dd++) {
                    // 构造日期 YYYYMMDD
                    long testSopDate = year * 10000L + mm * 100L + dd;
                    
                    // 测试SOP密码算法: result = sopDate * monthDayHour; password = result % 1000000
                    long sopResult = (testSopDate * monthDayHour) % 1000000;
                    
                    // 测试拨号密码算法: result = data * sopDate - hour; password = (result % 1000000000) % 1000000
                    long dialTempResult = monthDayHour * testSopDate - hour;
                    long dialResult = (dialTempResult % 1000000000L) % 1000000L;
                    
                    // 检查是否匹配
                    if (sopResult == sopPassword) {
                        System.out.println("★ SOP密码匹配! SOP日期: " + testSopDate);
                        System.out.println("  验证: " + testSopDate + " * " + monthDayHour + " % 1000000 = " + sopResult);
                        
                        // 同时检查拨号密码
                        System.out.println("  该日期的拨号密码: " + dialResult + (dialResult == dialPassword ? " (也匹配!)" : ""));
                        System.out.println();
                    }
                    
                    if (dialResult == dialPassword) {
                        System.out.println("★ 拨号密码匹配! SOP日期: " + testSopDate);
                        System.out.println("  验证: ((" + monthDayHour + " * " + testSopDate + " - " + hour + ") % 1000000000) % 1000000 = " + dialResult);
                        
                        // 同时检查SOP密码
                        System.out.println("  该日期的SOP密码: " + sopResult + (sopResult == sopPassword ? " (也匹配!)" : ""));
                        System.out.println();
                    }
                }
            }
        }
        
        System.out.println("=== 方法2：数学计算 ===");
        
        // 对于SOP密码: sopDate * 80212 ≡ 872920 (mod 1000000)
        // 需要找到 sopDate 使得等式成立
        
        System.out.println("SOP密码算法分析:");
        System.out.println("需要找到 sopDate，使得: sopDate * 80212 ≡ 872920 (mod 1000000)");
        
        // 尝试一些可能的值
        for (long k = 0; k < 1000; k++) {
            long numerator = sopPassword + k * 1000000L;
            if (numerator % monthDayHour == 0) {
                long sopDate = numerator / monthDayHour;
                if (sopDate >= 20200101L && sopDate <= 20301231L) {
                    System.out.println("可能的SOP日期: " + sopDate);
                    
                    // 验证
                    long verify = (sopDate * monthDayHour) % 1000000;
                    System.out.println("验证: " + verify + (verify == sopPassword ? " ✓" : " ✗"));
                }
            }
        }
        
        System.out.println();
        System.out.println("拨号密码算法分析:");
        System.out.println("需要找到 sopDate，使得: ((sopDate * 80212 - 12) % 1000000000) % 1000000 = 872908");
        
        // 对于拨号密码算法的逆向计算
        for (long k = 0; k < 100; k++) {
            for (long j = 0; j < 100; j++) {
                long numerator = dialPassword + hour + k * 1000000000L + j * 1000000L;
                if (numerator % monthDayHour == 0) {
                    long sopDate = numerator / monthDayHour;
                    if (sopDate >= 20200101L && sopDate <= 20301231L) {
                        System.out.println("可能的SOP日期: " + sopDate);
                        
                        // 验证
                        long tempResult = monthDayHour * sopDate - hour;
                        long verify = (tempResult % 1000000000L) % 1000000L;
                        System.out.println("验证: " + verify + (verify == dialPassword ? " ✓" : " ✗"));
                        
                        if (verify == dialPassword) {
                            break;
                        }
                    }
                }
            }
        }
    }
}
