# 工厂模式加密区密码生成文档

## 一、密码生成算法原理

密码是基于当前日期时间和预定义的SOP日期(Start Of Production日期)动态生成的6位数字密码。密码每小时会变化。

### 基本算法步骤：

1. 获取当前日期的月、日、小时
2. 计算 `data` = (月 × 10000) + (日 × 100) + 小时
3. 计算 `result` = SOP日期 × data
4. 生成密码 = result % 1000000 的6位数格式（不足6位前面补0）

## 二、SOP日期常量

系统中预定义了两个SOP日期常量：
- 默认SOP日期：`20230830`
- MY1项目SOP日期：`20250110`

系统根据项目类型自动选择相应的SOP日期：
- 如果系统属性`ro.product.build.chery.extend.project`值为`MY1`，使用MY1项目SOP日期
- 否则使用默认SOP日期

## 三、代码实现示例

以下是Java实现代码：

```java
import java.time.LocalDateTime;

public class PasswordGenerator {
    private static final int MY1_SOP_DATE = 20250110;
    private static final int SOP_DATE = 20230830;
    
    public static String generatePassword() {
        String projectType = getProjectType(); // 获取项目类型
        if ("MY1".equals(projectType)) {
            return getPasswordBySopDate(MY1_SOP_DATE);
        }
        return getPasswordBySopDate(SOP_DATE);
    }
    
    private static String getPasswordBySopDate(int sopDate) {
        LocalDateTime now = LocalDateTime.now();
        int month = now.getMonthValue();
        int day = now.getDayOfMonth();
        int hour = now.getHour();
        
        long data = (month * 10000) + (day * 100) + hour;
        long result = sopDate * data;
        
        return String.format("%06d", result % 1000000);
    }
    
    private static String getProjectType() {
        // 在实际应用中，这里读取系统属性
        // return SystemProperties.get("ro.product.build.chery.extend.project", "");
        
        // 示例中返回空字符串，表示使用默认SOP日期
        return "";
    }
}
```

## 四、密码计算示例

假设今天是2023年11月30日15点，且使用默认SOP日期：

1. 月 = 11
2. 日 = 30
3. 小时 = 15
4. data = (11 × 10000) + (30 × 100) + 15 = 110000 + 3000 + 15 = 113015
5. result = 20230830 × 113015 = 2286447882950
6. 密码 = 2286447882950 % 1000000 = 882950
7. 格式化为6位数：`882950`

## 五、密码更新周期

- 密码每小时更新一次
- 即使在同一天内，每小时密码也会变化
- 不同月份、不同日期的密码也会不同

## 六、不同项目密码比较

当前系统支持两种项目类型，它们的密码生成使用不同的SOP日期：

| 项目类型 | SOP日期 | 备注 |
|---------|--------|------|
| 默认    | 20230830 | 当系统属性未指定为MY1时使用 |
| MY1     | 20250110 | 当系统属性指定为MY1时使用 |

## 七、密码验证流程

1. 系统根据当前日期时间和相应项目的SOP日期生成密码
2. 用户输入密码
3. 系统比较用户输入与生成的密码是否匹配
4. 如匹配成功，允许访问加密区域
5. 如匹配失败，提示"密码错误"

---

注：本文档仅用于开发测试，请勿在未授权情况下使用此信息。 