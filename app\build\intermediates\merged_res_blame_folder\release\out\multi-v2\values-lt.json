{"logs": [{"outputFile": "com.autolink.any.app-mergeReleaseResources-29:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c040f7fd3bcdbcca32f80023251103f6\\transformed\\appcompat-1.7.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,9848", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,9927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dc8b32dcc042d5269c0b3f891bacfc7e\\transformed\\material-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1160,1226,1325,1403,1468,1578,1641,1713,1772,1846,1907,1961,2085,2146,2208,2262,2340,2474,2562,2639,2732,2813,2897,3038,3117,3201,3344,3441,3518,3574,3628,3694,3769,3848,3919,3999,4075,4153,4226,4303,4410,4497,4578,4668,4760,4832,4913,5005,5060,5142,5208,5293,5380,5442,5506,5569,5641,5752,5868,5969,6078,6138,6196,6278,6364,6440", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1155,1221,1320,1398,1463,1573,1636,1708,1767,1841,1902,1956,2080,2141,2203,2257,2335,2469,2557,2634,2727,2808,2892,3033,3112,3196,3339,3436,3513,3569,3623,3689,3764,3843,3914,3994,4070,4148,4221,4298,4405,4492,4573,4663,4755,4827,4908,5000,5055,5137,5203,5288,5375,5437,5501,5564,5636,5747,5863,5964,6073,6133,6191,6273,6359,6435,6513"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3194,3273,3351,3434,3528,4369,4465,4583,4667,4730,4796,4895,4973,5038,5148,5211,5283,5342,5416,5477,5531,5655,5716,5778,5832,5910,6044,6132,6209,6302,6383,6467,6608,6687,6771,6914,7011,7088,7144,7198,7264,7339,7418,7489,7569,7645,7723,7796,7873,7980,8067,8148,8238,8330,8402,8483,8575,8630,8712,8778,8863,8950,9012,9076,9139,9211,9322,9438,9539,9648,9708,9766,9932,10018,10094", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "420,3268,3346,3429,3523,3613,4460,4578,4662,4725,4791,4890,4968,5033,5143,5206,5278,5337,5411,5472,5526,5650,5711,5773,5827,5905,6039,6127,6204,6297,6378,6462,6603,6682,6766,6909,7006,7083,7139,7193,7259,7334,7413,7484,7564,7640,7718,7791,7868,7975,8062,8143,8233,8325,8397,8478,8570,8625,8707,8773,8858,8945,9007,9071,9134,9206,9317,9433,9534,9643,9703,9761,9843,10013,10089,10167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\27002045da22e0ff64c1cb4d22885da0\\transformed\\core-1.13.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "40,41,42,43,44,45,46,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3618,3716,3826,3925,4028,4139,4249,10172", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3711,3821,3920,4023,4134,4244,4364,10268"}}]}]}